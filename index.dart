
void main() {
  var a = new Set();
  a.addAll([1, 2, 3, 4, 5, 6]);
  a.forEach((value) => print(value));

  Map person = {"name": "zhangsan", "age": 18};

  person.forEach((key, value) => print("$key:$value"));

  printof();
  var f = su();
  print(f);








String pp=  printos("kb",sex: '女',age: 18);
print("$pp");


fangfa(printof);
var result = fangfa(() => printos("小明", sex: '男', age: 25));
print("通过fangfa调用的结果: $result");



















fangfa(()=>printos("6666怪",sex: '男',age:18));








}

//方法是可以客串的！！！！！随便接受和互传方法
void printof() {
  print("我是一个自信的");
  sum(10);
}

String printos(String kb,{String? sex='男',int? age}){
if(age!=null){
  return "名字$kb----性別$sex-----年齡$age";
}
return "名字$kb----性別$sex-----年齡保密";
}



su() {
  //return 的话要return不可用，要给它赋值
  //set与map都是要用{}
  Set i = {"1", "2", "3", "4"};
  return i;
}

//如果知道类型的话还是要加上类型好的
sum(n) {
  var zonghe = 0;
  for (int i = 0; i <= n; i++) {
    zonghe += i;
  }
  print(zonghe);
}


//没有指定类型就是方法
fangfa(fn){
return fn();



}